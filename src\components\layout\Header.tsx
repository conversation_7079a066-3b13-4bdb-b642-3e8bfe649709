import { useLocation } from 'wouter';
import { formatDate } from '@/lib/utils';
import { BellIcon, Bars3Icon } from '@heroicons/react/24/outline';
import { HomeIcon } from '@heroicons/react/24/solid';
import { Link } from 'wouter';

const breadcrumbMap: Record<string, string[]> = {
  '/dashboard': ['<PERSON>'],
  '/convicts': ['<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'Hüküm<PERSON><PERSON> Listesi'],
  '/convicts/new': ['<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Yönetimi', '<PERSON><PERSON>'],
  '/signatures/record': ['<PERSON><PERSON>za Kaydı'],
  '/reports/daily': ['<PERSON>or<PERSON>', 'Günlük Takip'],
  '/reports/convict-history': ['Rapor<PERSON>', 'Hükümlü Geçmişi'],
  '/reports/violations': ['Raporlar', 'İhlal Raporu'],
  '/admin/users': ['<PERSON><PERSON>net<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON> Yönetimi'],
  '/admin/settings': ['<PERSON><PERSON>net<PERSON>', 'Sistem Ayarları'],
};

export default function Header() {
  const [location] = useLocation();
  const today = new Date();

  const getBreadcrumbs = () => {
    // Handle dynamic routes
    if (location.includes('/convicts/') && location.includes('/edit')) {
      return ['Hükümlü Yönetimi', 'Hükümlü Düzenle'];
    }
    if (location.includes('/convicts/') && location.includes('/periods')) {
      return ['Hükümlü Yönetimi', 'İmza Periyotları'];
    }
    
    return breadcrumbMap[location] || ['Ana Sayfa'];
  };

  const breadcrumbs = getBreadcrumbs();

  return (
    <header className="bg-white/80 backdrop-blur-lg border-b border-slate-200/60 h-16 shadow-sm">
      <div className="flex items-center justify-between px-6 h-full">
        {/* Mobile menu button */}
        <button className="md:hidden p-2 rounded-xl hover:bg-slate-100 transition-colors">
          <Bars3Icon className="w-5 h-5 text-slate-600" />
        </button>
        
        {/* Breadcrumbs */}
        <nav className="flex" aria-label="Breadcrumb">
          <ol className="flex items-center space-x-3">
            <li>
              <div>
                <Link href="/dashboard" className="text-slate-500 hover:text-primary transition-colors">
                  <HomeIcon className="flex-shrink-0 h-5 w-5" />
                </Link>
              </div>
            </li>
            {breadcrumbs.map((breadcrumb, index) => (
              <li key={index}>
                <div className="flex items-center">
                  <svg
                    className="flex-shrink-0 h-4 w-4 text-slate-300"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 111.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="ml-3 text-sm font-medium text-slate-700">
                    {breadcrumb}
                  </span>
                </div>
              </li>
            ))}
          </ol>
        </nav>

        {/* Header Right Side */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <button className="relative p-2 rounded-xl hover:bg-slate-100 transition-colors group">
            <BellIcon className="w-5 h-5 text-slate-600 group-hover:text-slate-700" />
            <span className="absolute top-1.5 right-1.5 h-2 w-2 bg-red-500 rounded-full ring-2 ring-white"></span>
          </button>
          
          {/* Current Date */}
          <div className="hidden sm:flex items-center px-3 py-1.5 bg-slate-100/70 rounded-xl">
            <span className="text-sm font-medium text-slate-700">
              {formatDate(today, { 
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </span>
          </div>
        </div>
      </div>
    </header>
  );
}
