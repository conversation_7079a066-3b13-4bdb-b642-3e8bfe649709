import { useState, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useParams } from 'wouter';
import { Card, CardContent } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { ArrowLeftIcon, PrinterIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { Link } from 'wouter';
import { getConvictWithDetails } from '../../lib/tauri-api';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { formatDate } from '../../lib/utils';
import { generateSignatureDates, formatPeriodDisplay, groupSignaturePeriods } from '../../lib/signature-dates';
import type { SignaturePeriod } from '../../shared/schema';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { save } from '@tauri-apps/plugin-dialog';
import { writeFile } from '@tauri-apps/plugin-fs';
import './SignatureForm.css';

export default function SignatureFormPage() {
  const { id } = useParams<{ id: string }>();
  const convictId = parseInt(id!, 10);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 24; // 24 signatures per page

  // PDF generation state
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  // Ref for the printable content
  const printContentRef = useRef<HTMLDivElement>(null);

  // Fetch convict details
  const { data: convict, isLoading, error } = useQuery({
    queryKey: ['convict-details', convictId],
    queryFn: () => getConvictWithDetails(convictId),
    enabled: !!convictId,
  });

  // Helper function to create a signature form page for a specific page of dates
  const createSignatureFormPage = (pageSignatureDates: string[], pageNumber: number) => {
    const pageElement = document.createElement('div');
    pageElement.className = 'signature-form-page';
    pageElement.style.width = '595px'; // A4 width in pixels at 96 DPI
    pageElement.style.minHeight = '842px'; // A4 height in pixels at 96 DPI
    pageElement.style.margin = '0';
    pageElement.style.padding = '0';
    pageElement.style.fontFamily = "'Inter', sans-serif";
    pageElement.style.fontSize = '10px';
    pageElement.style.lineHeight = '1.2';
    pageElement.style.color = '#000';
    pageElement.style.position = 'relative';
    pageElement.style.backgroundColor = '#ffffff';

    pageElement.innerHTML = `
      <!-- Document Title -->
      <div style="position: absolute; top: -20px; left: 50%; transform: translateX(-50%); font-size: 10px; font-weight: 700; text-align: center; letter-spacing: 1px;">
        YÜKÜMLÜ İMZA ÇİZELGESİ
      </div>

      <!-- Header Section -->
      <div style="display: flex; width: 100%; height: 240px; border-top: 0.5px solid #000;">
        <!-- Left Section - Labels -->
        <div style="width: 31px; background-color: #DCDDEA; display: flex; align-items: center; justify-content: center; border-right: 0.5px solid #000; border-bottom: 0.5px solid #000;">
          <div style="font-size: 12px; font-weight: 700; writing-mode: vertical-rl; text-orientation: mixed; text-align: center; padding: 10px 0;">
            Yükümlünün
          </div>
        </div>

        <!-- Middle Section - Field Labels -->
        <div style="width: 161px; background-color: #DCDDEA; border-right: 0.5px solid #000; border-bottom: 0.5px solid #000;">
          <div style="height: 40px; border-bottom: 0.5px solid #000; display: flex; align-items: center; padding: 0 11px; font-size: 10px; font-weight: 700;">Dosya Numarası</div>
          <div style="height: 40px; border-bottom: 0.5px solid #000; display: flex; align-items: center; padding: 0 11px; font-size: 10px; font-weight: 700;">Adı Soyadı</div>
          <div style="height: 40px; border-bottom: 0.5px solid #000; display: flex; align-items: center; padding: 0 11px; font-size: 10px; font-weight: 700;">T.C. Kimlik Numarası</div>
          <div style="height: 40px; border-bottom: 0.5px solid #000; display: flex; align-items: center; padding: 0 11px; font-size: 10px; font-weight: 700;">Telefon Numarası</div>
          <div style="height: 40px; border-bottom: 0.5px solid #000; display: flex; align-items: center; padding: 0 11px; font-size: 10px; font-weight: 700;">Yakınına Ait Telefon Numarası</div>
          <div style="height: 40px; display: flex; align-items: center; padding: 0 11px; font-size: 10px; font-weight: 700;">İkamet Adresi</div>
        </div>

        <!-- Right Section - Field Values -->
        <div style="width: 161px; background-color: #ffffff; border-right: 0.5px solid #000; border-bottom: 0.5px solid #000;">
          <div style="height: 40px; border-bottom: 0.5px solid #000; display: flex; align-items: center; padding: 0 11px; font-size: 10px;">${convict.file_number || '-'}</div>
          <div style="height: 40px; border-bottom: 0.5px solid #000; display: flex; align-items: center; padding: 0 11px; font-size: 10px;">${convict.first_name.toUpperCase()} ${convict.last_name.toUpperCase()}</div>
          <div style="height: 40px; border-bottom: 0.5px solid #000; display: flex; align-items: center; padding: 0 11px; font-size: 10px;">${convict.tc_no}</div>
          <div style="height: 40px; border-bottom: 0.5px solid #000; display: flex; align-items: center; padding: 0 11px; font-size: 10px;">${convict.phone_number || '-'}</div>
          <div style="height: 40px; border-bottom: 0.5px solid #000; display: flex; align-items: center; padding: 0 11px; font-size: 10px;">${convict.relative_phone_number || '-'}</div>
          <div style="height: 40px; display: flex; align-items: center; padding: 0 11px; font-size: 10px;">${convict.address ? convict.address.replace(/\n/g, '<br>') : '-'}</div>
        </div>

        <!-- Signature Schedule Section -->
        <div style="width: 31px; background-color: #DCDDEA; display: flex; align-items: center; justify-content: center; border-right: 0.5px solid #000; border-bottom: 0.5px solid #000;">
          <div style="font-size: 12px; font-weight: 700; writing-mode: vertical-rl; text-orientation: mixed; text-align: center; padding: 10px 0;">
            İmza Gün ve Saatleri
          </div>
        </div>

        <div style="width: 107px; background-color: #ffffff; border-right: 0.5px solid #000; border-bottom: 0.5px solid #000; display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center; padding: 10px;">
          ${signaturePeriods.map(period => `
            <div style="font-size: 8px; font-weight: normal; line-height: 1.3; text-align: center; margin: 1px 0;">
              ${period.startDate} - ${period.endDate}
            </div>
            <div style="font-size: 8px; font-weight: normal; line-height: 1.3; text-align: center; margin: 1px 0;">
              ${period.frequency}
            </div>
            ${period.days ? `<div style="font-size: 8px; font-weight: normal; line-height: 1.3; text-align: center; margin: 1px 0;">${period.days}</div>` : ''}
            <div style="font-size: 8px; font-weight: normal; line-height: 1.3; text-align: center; margin: 1px 0;">
              ${period.time}
            </div>
          `).join('')}
        </div>

        <!-- Signature Sample Section -->
        <div style="width: 104px; display: flex; flex-direction: column; align-items: center; justify-content: start; text-align: center; gap: 40px; border-bottom: 0.5px solid #000;">
          <div style="width: 104px; height: 89px; border: 0.5px solid #000; display: flex; align-items: center; justify-content: center; margin-top: 20px;">
            <div style="font-size: 40px; font-weight: 700; color: #000;">1</div>
          </div>
          <div style="font-size: 10px; font-weight: 400; text-align: center; line-height: 1.2;">
            YÜKÜMLÜNÜN<br>İMZA ÖRNEĞİ
          </div>
        </div>
      </div>

      <!-- Table Section -->
      <div style="width: 100%; margin-top: 0px;">
        <!-- Table Headers -->
        <div style="width: 100%;">
          <div style="display: flex; width: 100%; height: 22px; background-color: #DCDDEA;">
            <div style="width: 318px; border: 0.5px solid #000; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: 400;">YÜKÜMLÜNÜN</div>
            <div style="width: 277px; border: 0.5px solid #000; display: flex; align-items: center; justify-content: center; font-size: 12px; font-weight: 400;">GÖREVLİNİN</div>
          </div>
          <div style="display: flex; width: 100%; height: 22px; background-color: #DCDDEA;">
            <div style="width: 31px; border: 0.5px solid #000; display: flex; align-items: center; justify-content: center; font-size: 10px; font-weight: 700; text-align: center;">S.N.</div>
            <div style="width: 131px; border: 0.5px solid #000; display: flex; align-items: center; justify-content: center; font-size: 10px; font-weight: 700; text-align: center;">İMZA TARİHİ</div>
            <div style="width: 75px; border: 0.5px solid #000; display: flex; align-items: center; justify-content: center; font-size: 10px; font-weight: 700; text-align: center;">İMZA SAATİ</div>
            <div style="width: 81px; border: 0.5px solid #000; display: flex; align-items: center; justify-content: center; font-size: 10px; font-weight: 700; text-align: center;">İMZASI</div>
            <div style="width: 146px; border: 0.5px solid #000; display: flex; align-items: center; justify-content: center; font-size: 10px; font-weight: 700; text-align: center;">ADI SOYADI/ÜNVANI</div>
            <div style="width: 131px; border: 0.5px solid #000; display: flex; align-items: center; justify-content: center; font-size: 10px; font-weight: 700; text-align: center;">İMZASI</div>
          </div>
        </div>

        <!-- Table Rows -->
        <div>
          ${pageSignatureDates.map((date, index) => `
            <div style="display: flex; width: 100%; height: 25px;">
              <div style="width: 31px; border: 0.5px solid #000; display: flex; align-items: center; justify-content: center; font-size: 10px;">${(pageNumber - 1) * itemsPerPage + index + 1}</div>
              <div style="width: 131px; border: 0.5px solid #000; display: flex; align-items: center; justify-content: center; font-size: 10px;">${date}</div>
              <div style="width: 75px; border: 0.5px solid #000;"></div>
              <div style="width: 81px; border: 0.5px solid #000;"></div>
              <div style="width: 146px; border: 0.5px solid #000;"></div>
              <div style="width: 131px; border: 0.5px solid #000;"></div>
            </div>
          `).join('')}
        </div>
      </div>
    `;

    return pageElement;
  };

  const handlePrint = async () => {
    if (!convict) {
      return;
    }

    try {
      setIsGeneratingPDF(true);

      // PDF oluştur
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
      });

      // A4 boyutları (mm) - margin'siz tam boyut
      const pdfWidth = 210;
      const pdfHeight = 297;

      // Tüm sayfaları işle
      for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
        const startIndex = (pageNum - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const pageSignatureDates = allSignatureDates.slice(startIndex, endIndex);

        // Sayfa elementini oluştur
        const pageElement = createSignatureFormPage(pageSignatureDates, pageNum);

        // Geçici olarak DOM'a ekle
        document.body.appendChild(pageElement);

        // Canvas oluştur
        const canvas = await html2canvas(pageElement, {
          scale: 3, // Yüksek kalite için
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          width: 595, // A4 width in pixels
          height: 842, // A4 height in pixels
        });

        // DOM'dan kaldır
        document.body.removeChild(pageElement);

        // İlk sayfa değilse yeni sayfa ekle
        if (pageNum > 1) {
          pdf.addPage();
        }

        // Görüntüyü PDF'e ekle - tam boyut, margin'siz
        const imgData = canvas.toDataURL('image/png');
        pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
      }

      // Dosya kaydetme dialogi göster
      const fileName = `İmza_Föyü_${convict.first_name}_${convict.last_name}_${formatDate(new Date()).replace(/\./g, '_')}.pdf`;

      const filePath = await save({
        filters: [
          {
            name: 'PDF Dosyaları',
            extensions: ['pdf'],
          },
        ],
        defaultPath: fileName,
      });

      if (filePath) {
        // PDF'i ArrayBuffer olarak al
        const pdfArrayBuffer = pdf.output('arraybuffer');
        // Uint8Array'e dönüştür
        const pdfUint8Array = new Uint8Array(pdfArrayBuffer);

        // Dosyayı kaydet
        await writeFile(filePath, pdfUint8Array);

        console.log('PDF başarıyla kaydedildi:', filePath);
      }

    } catch (error) {
      console.error('PDF oluşturma hatası:', error);
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  if (isLoading) {
    return (
      <div className="windows-content">
        <div className="flex flex-col items-center justify-center py-8">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Hükümlü bilgileri yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (error || !convict) {
    return (
      <div className="windows-content">
        <Card className="windows-card">
          <CardContent className="text-center py-8">
            <p className="text-red-600">Hükümlü bilgileri yüklenirken bir hata oluştu.</p>
            <Link href="/convicts">
              <Button variant="windows" className="mt-4">
                <ArrowLeftIcon className="w-4 h-4 mr-2" />
                Hükümlü Listesine Dön
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Group signature periods with same date range, type, and time
  const groupedPeriods = groupSignaturePeriods(convict.activePeriods || []);

  // Process signature periods for display using the new utility
  const signaturePeriods = groupedPeriods.map((period: SignaturePeriod) => {
    const { frequency, days, time } = formatPeriodDisplay(period);
    // Use actual time range from database if available
    const actualTime = (period.time_start && period.time_end) 
      ? `Saat ${period.time_start} - ${period.time_end}`
      : time;
    
    return {
      startDate: formatDate(period.start_date),
      endDate: formatDate(period.end_date),
      frequency,
      days,
      time: actualTime
    };
  }) || [];

  // Generate signature dates using the new utility
  // Calculate appropriate number of dates based on periods
  const activePeriods = convict.activePeriods || [];
  
  // Calculate total period duration to determine number of pages needed
  let totalDatesNeeded = 24; // Default minimum
  
  if (activePeriods.length > 0) {
    // Estimate dates needed based on frequency
    let estimatedSignatureDays = 0;
    activePeriods.forEach(period => {
      const periodDays = Math.ceil((new Date(period.end_date).getTime() - new Date(period.start_date).getTime()) / (1000 * 60 * 60 * 24));
      
      if (period.frequency_type === 'WEEKLY') {
        const weeklyDays = period.frequency_value.split(',').length;
        estimatedSignatureDays += Math.ceil(periodDays / 7) * weeklyDays;
      } else if (period.frequency_type === 'X_DAYS') {
        const interval = parseInt(period.frequency_value) || 1;
        estimatedSignatureDays += Math.ceil(periodDays / interval);
      } else if (period.frequency_type === 'MONTHLY_SPECIFIC') {
        const monthlyDays = period.frequency_value.split(',').length;
        const months = Math.ceil(periodDays / 30);
        estimatedSignatureDays += months * monthlyDays;
      }
    });
    
    // Use estimated days but ensure minimum of 24 and maximum reasonable limit
    totalDatesNeeded = Math.max(24, Math.min(estimatedSignatureDays, 200));
  }
  
  const generatedDates = generateSignatureDates(activePeriods, totalDatesNeeded, false); // Start from period start, not today
  const allSignatureDates = generatedDates.map(d => d.date);
  
  // Calculate pagination
  const totalPages = Math.ceil(allSignatureDates.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const signatureDates = allSignatureDates.slice(startIndex, endIndex);
  
  // Calculate date range for current page
  const pageStartDate = signatureDates.length > 0 ? signatureDates[0] : '';
  const pageEndDate = signatureDates.length > 0 ? signatureDates[signatureDates.length - 1] : '';

  // Calculate summary statistics (for future use)
  // const periodSummary = convict.activePeriods?.reduce((summary, period) => {
  //   const type = period.frequency_type;
  //   summary[type] = (summary[type] || 0) + 1;
  //   return summary;
  // }, {} as Record<string, number>) || {};

  return (
    <>
      {/* Print/Navigation Controls - Hidden in print */}
      <div className="windows-content print:hidden">
        <div className="windows-toolbar-secondary">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="windows-title">
                İmza Föyü
              </h1>
              <p className="windows-subtitle">
                {convict.first_name} {convict.last_name} - TC: {convict.tc_no}
                {totalPages > 1 && (
                  <span className="ml-4">
                    (Sayfa {currentPage}/{totalPages} - {pageStartDate} / {pageEndDate})
                  </span>
                )}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              {/* Pagination Controls - Only show if more than 1 page */}
              {totalPages > 1 && (
                <div className="flex items-center space-x-1 mr-4">
                  <Button
                    variant="windows"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeftIcon className="w-4 h-4" />
                  </Button>
                  <span className="text-xs px-2 py-1 bg-gray-100 rounded">
                    {currentPage}/{totalPages}
                  </span>
                  <Button
                    variant="windows"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                  >
                    <ChevronRightIcon className="w-4 h-4" />
                  </Button>
                </div>
              )}
              
              <Button
                variant="windows-primary"
                size="sm"
                onClick={handlePrint}
                disabled={isGeneratingPDF}
              >
                <PrinterIcon className="w-4 h-4 mr-2" />
                {isGeneratingPDF ? 'PDF Oluşturuluyor...' : 'PDF Olarak Kaydet'}
              </Button>
              <Link href="/convicts">
                <Button variant="windows" size="sm">
                  <ArrowLeftIcon className="w-4 h-4 mr-2" />
                  Listeye Dön
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Signature Form Document - Styled for A4 printing */}
      <div
        ref={printContentRef}
        className="signature-form-document print:p-0 print:m-0 print:shadow-none print:border-none p-8"
      >
        <div className="signature-form-page">
          {/* Document Title - Positioned at bottom center */}
          <div className="signature-form-title">
            YÜKÜMLÜ İMZA ÇİZELGESİ
          </div>
          {/* Header Section */}
          <div className="signature-form-header">
            {/* Left Section - Labels */}
            <div className="signature-form-header-section signature-form-header-label">
              <div className="signature-form-header-title">
                Yükümlünün
              </div>
            </div>

            {/* Middle Section - Field Labels */}
            <div className="signature-form-header-section signature-form-header-fields">
              <div className="signature-form-field">
                <div className="signature-form-field-label">Dosya Numarası</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">Adı Soyadı</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">T.C. Kimlik Numarası</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">Telefon Numarası</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">Yakınına Ait Telefon Numarası</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-label">İkamet Adresi</div>
              </div>
            </div>

            {/* Right Section - Field Values */}
            <div className="signature-form-header-section signature-form-header-values">
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.file_number || '-'}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.first_name.toUpperCase()} {convict.last_name.toUpperCase()}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.tc_no}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.phone_number || '-'}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">{convict.relative_phone_number || '-'}</div>
              </div>
              <div className="signature-form-field">
                <div className="signature-form-field-value">
                  {convict.address ? (
                    convict.address.split('\n').map((line, index) => (
                      <span key={index}>
                        {line}
                        {index < convict.address!.split('\n').length - 1 && <br />}
                      </span>
                    ))
                  ) : (
                    '-'
                  )}
                </div>
              </div>
            </div>

            {/* Signature Schedule Section */}
            <div className="signature-form-header-section signature-form-schedule-label">
              <div className="signature-form-header-title">
                İmza Gün ve Saatleri
              </div>
            </div>

            <div className="signature-form-header-section signature-form-schedule-values">
              <div className="signature-form-schedule">
                {signaturePeriods.map((period, index) => (
                  <div key={index} className="signature-form-schedule-item">
                    <div className="signature-form-schedule-text">
                      {period.startDate} - {period.endDate}
                    </div>
                    <div className="signature-form-schedule-text">
                      {period.frequency}
                    </div>
                    {period.days && (
                      <div className="signature-form-schedule-text">
                        {period.days}
                      </div>
                    )}
                    <div className="signature-form-schedule-text">
                      {period.time}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Signature Sample Section */}
            <div className="signature-form-header-section signature-form-sample">
              <div className="signature-form-sample-box">
                <div className="signature-form-sample-number">1</div>
              </div>
              <div className="signature-form-sample-label">
                YÜKÜMLÜNÜN
                <br />
                İMZA ÖRNEĞİ
              </div>
            </div>
          </div>

          {/* Table Section */}
          <div className="signature-form-table">
            {/* Table Headers */}
            <div className="signature-form-table-headers">
              <div className="signature-form-table-section-labels">
                <div className="signature-form-table-section-label signature-form-table-section-convict">YÜKÜMLÜNÜN</div>
                <div className="signature-form-table-section-label signature-form-table-section-officer">GÖREVLİNİN</div>
              </div>
              <div className="signature-form-table-header-section">
                <div className="signature-form-table-header signature-form-table-header-sn">S.N.</div>
                <div className="signature-form-table-header signature-form-table-header-date">İMZA TARİHİ</div>
                <div className="signature-form-table-header signature-form-table-header-time">İMZA SAATİ</div>
                <div className="signature-form-table-header signature-form-table-header-convict-sig">İMZASI</div>
                <div className="signature-form-table-header signature-form-table-header-officer-name">ADI SOYADI/ÜNVANI</div>
                <div className="signature-form-table-header signature-form-table-header-officer-sig">İMZASI</div>
              </div>
            </div>

            {/* Table Rows */}
            <div className="signature-form-table-rows">
              {signatureDates.map((date, index) => (
                <div key={index} className="signature-form-table-row">
                  <div className="signature-form-table-cell signature-form-table-cell-sn">
                    {index + 1}
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-date">
                    {date}
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-time">
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-convict-sig">
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-officer-name">
                  </div>
                  <div className="signature-form-table-cell signature-form-table-cell-officer-sig">
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
