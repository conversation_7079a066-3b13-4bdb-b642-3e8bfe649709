import { useQuery } from '@tanstack/react-query';
import { useParams } from 'wouter';
import { Card, CardContent } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { ArrowLeftIcon, PrinterIcon, PencilIcon, CheckIcon } from '@heroicons/react/24/outline';
import { Link } from 'wouter';
import { getConvictWithDetails } from '../../lib/tauri-api';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import { formatDate } from '../../lib/utils';
import { useState, useRef } from 'react';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { save } from '@tauri-apps/plugin-dialog';
import { writeFile } from '@tauri-apps/plugin-fs';

import './ViolationRecord.css';

export default function ViolationRecordPage() {
  const { id } = useParams<{ id: string }>();
  const convictId = parseInt(id!, 10);

  // State for editing
  const [isEditing, setIsEditing] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  
  // Ref for the printable content
  const printContentRef = useRef<HTMLDivElement>(null);
  
  
  
  const [editedContent, setEditedContent] = useState({
    title: 'T U T A N A K',
    mainText: 'Fethiye Denetimli Serbestlik Müdürlüğünün {tarih} tarih ve {dosyaNumarasi} sayılı yazıları gereği imza atma yükümlülüğü olan {adiSoyadi} isimli şahsın {imzaTarihi} tarihinde/tarihlerinde imza atmadığı tespit edilmiştir.',
    closingText: 'İş bu tutanak birlikte imza altına alındı.',
    leftSignatureLabel: '',
    rightSignatureLabel: ''
  });

  // Fetch convict details
  const { data: convict, isLoading, error } = useQuery({
    queryKey: ['convict-details', convictId],
    queryFn: () => getConvictWithDetails(convictId),
    enabled: !!convictId,
  });

  const handlePrint = async () => {
    if (!convict || !printContentRef.current) {
      return;
    }
    
    try {
      setIsGeneratingPDF(true);
      
      // Geçici olarak yazdırma moduna geç
      document.body.classList.add('print-mode');
      
      // Canvas oluştur
      const canvas = await html2canvas(printContentRef.current, {
        scale: 3, // Yüksek kalite için
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: printContentRef.current.scrollWidth,
        height: printContentRef.current.scrollHeight,
      });

      // PDF oluştur
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
      });

      // A4 boyutları (mm) - tam boyut, margin'siz
      const pdfWidth = 210;
      const pdfHeight = 297;

      // Görüntü boyutlarını hesapla - tam A4 boyutunu kullan
      const imgWidth = pdfWidth; // Margin'siz tam genişlik
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      // Eğer yükseklik A4'ten fazlaysa, yüksekliği A4'e sığdır
      if (imgHeight > pdfHeight) {
        const scaledHeight = pdfHeight;
        const scaledWidth = (canvas.width * scaledHeight) / canvas.height;
        pdf.addImage(imgData, 'PNG', 0, 0, scaledWidth, scaledHeight);
      } else {
        // Görüntüyü PDF'e ekle - margin'siz (0,0 koordinatından başla)
        pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
      }
      
      // Dosya kaydetme dialogi göster
      const fileName = `İhlal_Tutanaği_${convict.first_name}_${convict.last_name}_${formatDate(new Date()).replace(/\./g, '_')}.pdf`;
      
      const filePath = await save({
        filters: [
          {
            name: 'PDF Dosyaları',
            extensions: ['pdf'],
          },
        ],
        defaultPath: fileName,
      });

      if (filePath) {
        // PDF'i ArrayBuffer olarak al
        const pdfArrayBuffer = pdf.output('arraybuffer');
        // Uint8Array'e dönüştür
        const pdfUint8Array = new Uint8Array(pdfArrayBuffer);
        
        // Dosyayı kaydet
        await writeFile(filePath, pdfUint8Array);
        
        console.log('PDF başarıyla kaydedildi:', filePath);
      }
      
      // Print modunu kaldır
      document.body.classList.remove('print-mode');
      
    } catch (error) {
      console.error('PDF oluşturma hatası:', error);
      document.body.classList.remove('print-mode');
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const handleEdit = () => {
    setIsEditing(!isEditing);
  };

  const handleSave = () => {
    setIsEditing(false);
    // Here you could save to database or local storage
  };

  if (isLoading) {
    return (
      <div className="windows-content">
        <div className="flex flex-col items-center justify-center py-8">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">Hükümlü bilgileri yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (error || !convict) {
    return (
      <div className="windows-content">
        <Card className="windows-card">
          <CardContent className="text-center py-8">
            <p className="text-red-600">Hükümlü bilgileri yüklenirken bir hata oluştu.</p>
            <Link href="/convicts">
              <Button variant="windows" className="mt-4">
                <ArrowLeftIcon className="w-4 h-4 mr-2" />
                Hükümlü Listesine Dön
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentDate = new Date();
  const formattedDate = formatDate(currentDate);

  const FilledText: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => {
    return <span className={`${className || ''}`}>{children}</span>;
  };

  return (
    <div className="windows-content violation-record-page">
      {/* Screen Header - Hidden when printing */}
      <div className="print:hidden mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/convicts">
              <Button variant="windows">
                <ArrowLeftIcon className="w-4 h-4 mr-2" />
                Geri
              </Button>
            </Link>
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                Tutanak
              </h1>
              <p className="text-sm text-gray-600">
                {convict.first_name} {convict.last_name} - TC: {convict.tc_no}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              onClick={handlePrint}
              variant="windows"
              disabled={isGeneratingPDF}
            >
              <PrinterIcon className="w-4 h-4 mr-2" />
              {isGeneratingPDF ? 'PDF Oluşturuluyor...' : 'PDF Olarak Kaydet'}
            </Button>
            <Button onClick={isEditing ? handleSave : handleEdit} variant="windows">
              {isEditing ? (
                <>
                  <CheckIcon className="w-4 h-4 mr-2" />
                  Kaydet
                </>
              ) : (
                <>
                  <PencilIcon className="w-4 h-4 mr-2" />
                  Düzenle
                </>
              )}
            </Button>
          </div>
        </div>

        
      </div>

      {/* Document Content */}
      <div className="min-h-screen bg-gray-100 py-8 px-4 flex items-center justify-center">
        <div 
          ref={printContentRef}
          className={`violation-record-content bg-white text-black p-12 shadow-xl max-w-4xl w-full ${isEditing ? 'editing-mode' : ''}`} 
          style={{ fontFamily: "'Times New Roman', Times, serif" }}
        >
          {isEditing ? (
            <input
              type="text"
              value={editedContent.title}
              onChange={(e) => setEditedContent({...editedContent, title: e.target.value})}
              className="text-2xl font-bold text-center mb-12 tracking-wider w-full border-b border-gray-300 bg-transparent focus:outline-none focus:border-blue-500 edit-field print:border-none"
            />
          ) : (
            <h1 className="text-2xl font-bold text-center mb-12 tracking-wider">
              {editedContent.title}
            </h1>
          )}

          <div className="text-lg leading-relaxed space-y-6 mb-12 text-justify">
            {isEditing ? (
              <div className="space-y-4 print:hidden">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Ana Metin:</label>
                  <textarea
                    value={editedContent.mainText}
                    onChange={(e) => setEditedContent({...editedContent, mainText: e.target.value})}
                    className="w-full h-32 p-3 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500 edit-field"
                    placeholder="Ana tutanak metni..."
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Değişkenler: {"{imzaTarihi}"} - imza tarihi, {"{dosyaNumarasi}"} - dosya numarası, {"{adiSoyadi}"} - tam ad
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Kapanış Metni:</label>
                  <input
                    type="text"
                    value={editedContent.closingText}
                    onChange={(e) => setEditedContent({...editedContent, closingText: e.target.value})}
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:border-blue-500 edit-field"
                    placeholder="Kapanış metni..."
                  />
                </div>
              </div>
            ) : null}
            
            <div className={isEditing ? 'hidden print:block' : ''}>
              <p className="indent-8">
                {editedContent.mainText
                  .replace('{tarih}', formattedDate)
                  .replace('{dosyaNumarasi}', convict.file_number || '2015/134EKL')
                  .replace('{adiSoyadi}', `${convict.first_name} ${convict.last_name}`)
                  .replace('{imzaTarihi}', formattedDate)}
              </p>
              <p className="indent-8">
                {editedContent.closingText} <FilledText className="ml-2">{formattedDate.split('.').join('/')}</FilledText>
              </p>
            </div>
          </div>

          <div className="mt-20 pt-10 flex justify-between items-start">
            {/* Left Signature Area */}
            <div className="w-2/5 text-center">
              <div className="mb-4 h-16 flex items-center justify-center">
                {/* Placeholder for signature */}
                
              </div>
              <div className="signature-line">
                {isEditing ? (
                  <input
                    type="text"
                    value={editedContent.leftSignatureLabel}
                    onChange={(e) => setEditedContent({...editedContent, leftSignatureLabel: e.target.value})}
                    className="w-full text-center border-b border-gray-300 bg-transparent focus:outline-none focus:border-blue-500 edit-field print:border-none"
                    placeholder="Sol imza etiketi"
                  />
                ) : (
                  <span>{editedContent.leftSignatureLabel}</span>
                )}
              </div>
            </div>

            {/* Right Signature Area */}
            <div className="w-2/5 text-center">
              <div className="mb-4 h-16 flex items-center justify-center">
                {/* Placeholder for signature */}
                
              </div>
              <div className="signature-line">
                {isEditing ? (
                  <input
                    type="text"
                    value={editedContent.rightSignatureLabel}
                    onChange={(e) => setEditedContent({...editedContent, rightSignatureLabel: e.target.value})}
                    className="w-full text-center border-b border-gray-300 bg-transparent focus:outline-none focus:border-blue-500 edit-field print:border-none"
                    placeholder="Sağ imza etiketi"
                  />
                ) : (
                  <span>{editedContent.rightSignatureLabel}</span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
