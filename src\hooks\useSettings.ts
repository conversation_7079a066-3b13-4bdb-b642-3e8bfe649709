import { useEffect, useState } from 'react';
import { useSettingsStore, defaultSettings } from '@/store/settingsStore';
import { getAllSettings, convertSettingsToSystemSettings } from '@/lib/tauri-api';

/**
 * Hook to load and apply system settings
 */
export const useSettings = () => {
  const { settings, setSettings, setLoading } = useSettingsStore();
  const [isInitialized, setIsInitialized] = useState(false);

  // Load settings from database on mount (only once)
  useEffect(() => {
    if (isInitialized) return;

    const loadSettings = async () => {
      try {
        setLoading(true);
        const dbSettings = await getAllSettings();
        const systemSettings = convertSettingsToSystemSettings(dbSettings);

        // Merge with defaults to ensure all fields are present
        const mergedSettings = { ...defaultSettings, ...systemSettings };

        setSettings(mergedSettings);
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to load settings:', error);
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, [isInitialized, setSettings, setLoading]);

  // Apply CSS variables for animations and other UI settings
  useEffect(() => {
    const root = document.documentElement;
    
    // Apply animation settings
    if (settings.enableAnimations) {
      root.style.setProperty('--animation-duration', '200ms');
      root.style.setProperty('--animation-timing', 'ease-in-out');
    } else {
      root.style.setProperty('--animation-duration', '0ms');
      root.style.setProperty('--animation-timing', 'linear');
    }
    
    // Apply compact mode
    if (settings.compactMode) {
      root.classList.add('compact-mode');
      root.style.setProperty('--spacing-unit', '0.75rem');
      root.style.setProperty('--font-size-base', '0.875rem');
    } else {
      root.classList.remove('compact-mode');
      root.style.setProperty('--spacing-unit', '1rem');
      root.style.setProperty('--font-size-base', '1rem');
    }
    
    // Apply records per page to data tables
    root.style.setProperty('--records-per-page', settings.recordsPerPage.toString());
    
  }, [settings.enableAnimations, settings.compactMode, settings.recordsPerPage]);

  return {
    settings,
    isLoading: useSettingsStore(state => state.isLoading),
  };
};

/**
 * Hook to get organization info for headers and titles
 */
export const useOrganizationInfo = () => {
  const { settings } = useSettingsStore();
  
  return {
    systemName: settings.systemName,
    organizationName: settings.organizationName,
    systemLanguage: settings.systemLanguage,
  };
};

/**
 * Hook to get display preferences
 */
export const useDisplaySettings = () => {
  const { settings } = useSettingsStore();
  
  return {
    recordsPerPage: settings.recordsPerPage,
    enableAnimations: settings.enableAnimations,
    compactMode: settings.compactMode,
    showWelcomeMessage: settings.showWelcomeMessage,
  };
};

/**
 * Hook to get security settings
 */
export const useSecuritySettings = () => {
  const { settings } = useSettingsStore();
  
  return {
    sessionTimeoutMinutes: settings.sessionTimeoutMinutes,
    passwordMinLength: settings.passwordMinLength,
    requireStrongPassword: settings.requireStrongPassword,
    enableTwoFactorAuth: settings.enableTwoFactorAuth,
    maxLoginAttempts: settings.maxLoginAttempts,
    lockoutDurationMinutes: settings.lockoutDurationMinutes,
  };
};

/**
 * Hook to get signature settings
 */
export const useSignatureSettings = () => {
  const { settings } = useSettingsStore();
  
  return {
    signatureToleranceMinutes: settings.signatureToleranceMinutes,
    autoViolationDetection: settings.autoViolationDetection,
    requireReasonForLateSignature: settings.requireReasonForLateSignature,
    maxDailySignatures: settings.maxDailySignatures,
  };
};

/**
 * Hook to get notification settings
 */
export const useNotificationSettings = () => {
  const { settings } = useSettingsStore();
  
  return {
    enableEmailNotifications: settings.enableEmailNotifications,
    emailServer: settings.emailServer,
    emailPort: settings.emailPort,
    emailUsername: settings.emailUsername,
    emailPassword: settings.emailPassword,
    enableSystemNotifications: settings.enableSystemNotifications,
    notificationSound: settings.notificationSound,
  };
};
