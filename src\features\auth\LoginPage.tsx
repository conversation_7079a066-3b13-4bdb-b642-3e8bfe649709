import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { CheckCircleIcon } from '@heroicons/react/24/outline';
import { useAuthStore } from '@/store/authStore';
import { useSettingsStore } from '@/store/settingsStore';
import { authenticateUser } from '@/lib/tauri-api';
import { useLocation } from 'wouter';

const loginSchema = z.object({
  username: z.string().min(1, 'Kullanıcı adı gereklidir'),
  password: z.string().min(1, 'Şifre gereklidir'),
});

type LoginForm = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const [, setLocation] = useLocation();
  const [error, setError] = useState<string | null>(null);
  const { setUser, setLoading, isLoading } = useAuthStore();
  const { settings } = useSettingsStore();

  const form = useForm<LoginForm>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  const onSubmit = async (data: LoginForm) => {
    setLoading(true);
    setError(null);

    try {
      const user = await authenticateUser(data.username, data.password);
      
      if (user) {
        setUser(user);
        setLocation('/dashboard');
      } else {
        setError('Kullanıcı adı veya şifre hatalı');
        setLoading(false);
      }
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'Giriş yapılırken bir hata oluştu');
      setLoading(false);
    }
  };

  return (
    <div className="h-screen flex items-center justify-center windows-scrollbar" style={{backgroundColor: 'var(--windows-bg)'}}>
      <div className="w-96 windows-dialog">
        {/* Title Bar */}
        <div className="windows-dialog-title">
          <div className="flex items-center">
            <CheckCircleIcon className="h-4 w-4 mr-2" />
            <span className="text-xs">{settings.systemName}</span>
          </div>
        </div>
        
        {/* Content */}
        <div className="p-6" style={{backgroundColor: 'var(--windows-control-bg)'}}>
          <div className="text-center mb-6">
            <div className="mx-auto h-12 w-12 flex items-center justify-center mb-3" style={{backgroundColor: 'var(--windows-titlebar)'}}>
              <CheckCircleIcon className="h-8 w-8 text-white" />
            </div>
            <h2 className="text-sm font-normal mb-1" style={{color: 'var(--windows-text)'}}>Sisteme Giriş</h2>
            <p className="text-xs text-gray-600">Kullanıcı bilgilerinizi girin</p>
          </div>
          
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 text-xs">
              {error}
            </div>
          )}
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs" style={{color: 'var(--windows-text)'}}>Kullanıcı Adı:</FormLabel>
                    <FormControl>
                      <Input
                        className="windows-input w-full"
                        placeholder="Kullanıcı adınızı girin"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage className="text-xs text-red-600" />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-xs" style={{color: 'var(--windows-text)'}}>Şifre:</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        className="windows-input w-full"
                        placeholder="Şifrenizi girin"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage className="text-xs text-red-600" />
                  </FormItem>
                )}
              />
              
              <div className="pt-2">
                <Button
                  type="submit"
                  variant="windows-primary"
                  size="windows"
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading ? 'Giriş yapılıyor...' : 'Giriş Yap'}
                </Button>
              </div>
            </form>
          </Form>
          
          <div className="mt-4 p-3 border text-xs" style={{backgroundColor: 'white', borderColor: 'var(--windows-border)'}}>
            <p className="font-semibold mb-1" style={{color: 'var(--windows-text)'}}>Demo Giriş Bilgileri:</p>
            <p style={{color: 'var(--windows-text)'}}>Kullanıcı: <strong>admin</strong> | Şifre: <strong>password</strong></p>
          </div>
        </div>
      </div>
    </div>
  );
}
