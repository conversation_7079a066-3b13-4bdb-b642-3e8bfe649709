import { useState } from 'react';
import { useLocation } from 'wouter';
import { useAuthStore } from '@/store/authStore';
import { useUIStore } from '@/store/uiStore';
import { cn } from '@/lib/utils';

interface MenuItem {
  name: string;
  action: string;
  shortcut?: string;
  checked?: boolean;
  separator?: never;
}

interface MenuSeparator {
  separator: true;
  name?: never;
  action?: never;
  shortcut?: never;
  checked?: never;
}

type MenuItemType = MenuItem | MenuSeparator;

interface Menu {
  name: string;
  items: MenuItemType[];
}

export default function WindowsMenuBar() {
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const [, navigate] = useLocation();
  const { user, logout } = useAuthStore();
  const { isToolbarVisible, toggleToolbar, isStatusBarVisible, toggleStatusBar } = useUIStore();

  const menus: Menu[] = [
    {
      name: '<PERSON><PERSON><PERSON>',
      items: [
        { name: '<PERSON><PERSON>', action: 'new-convict' },
        { name: '<PERSON><PERSON><PERSON>', action: 'signature-record' },
        { separator: true },
        { name: 'Dışa Aktar...', action: 'export' },
        { name: 'Yazdır...', action: 'print', shortcut: 'Ctrl+P' },
        { separator: true },
        { name: 'Çıkış', action: 'exit', shortcut: 'Alt+F4' },
      ]
    },
    {
      name: 'Düzenle',
      items: [
        { name: 'Ara...', action: 'search', shortcut: 'Ctrl+F' },
        { name: 'Filtrele...', action: 'filter', shortcut: 'Ctrl+Shift+F' },
        { separator: true },
        { name: 'Tercihler...', action: 'preferences' },
      ]
    },
    {
      name: 'Görünüm',
      items: [
        { name: 'Yenile', action: 'refresh', shortcut: 'F5' },
        { separator: true },
        { name: 'Araç Çubuğu', action: 'toggle-toolbar', checked: isToolbarVisible },
        { name: 'Durum Çubuğu', action: 'toggle-statusbar', checked: isStatusBarVisible },
      ]
    },
    {
      name: 'Raporlar',
      items: [
        { name: 'Günlük Takip', action: 'daily-report' },
        { name: 'Hükümlü Geçmişi', action: 'convict-history' },
        { name: 'İhlal Raporu', action: 'violation-report' },
        { separator: true },
        { name: 'Rapor Ayarları...', action: 'report-settings' },
      ]
    },
    {
      name: 'Araçlar',
      items: [
        { name: 'Veri Yedekleme', action: 'backup' },
        { name: 'Veri Geri Yükleme', action: 'restore' },
        { separator: true },
        { name: 'Sistem Ayarları...', action: 'system-settings' },
      ]
    },
    {
      name: 'Yardım',
      items: [
        { name: 'Kullanım Kılavuzu', action: 'help', shortcut: 'F1' },
        { name: 'Klavye Kısayolları', action: 'shortcuts' },
        { separator: true },
        { name: 'Hakkında...', action: 'about' },
      ]
    }
  ];

  const handleMenuItemClick = (action: string) => {
    setActiveMenu(null);

    switch (action) {
      case 'new-convict':
        navigate('/convicts/new');
        break;
      case 'signature-record':
        navigate('/signatures/record');
        break;
      case 'daily-report':
        navigate('/reports/daily');
        break;
      case 'convict-history':
        navigate('/reports/convict-history');
        break;
      case 'violation-report':
        navigate('/reports/violations');
        break;
      case 'refresh':
        window.location.reload();
        break;
      case 'print':
        window.print();
        break;
      case 'exit':
        logout();
        break;
      case 'toggle-toolbar':
        toggleToolbar();
        break;      case 'toggle-statusbar':
        toggleStatusBar();
        break;
      case 'system-settings':
        navigate('/admin/settings');
        break;
      case 'help':
        navigate('/help/usage-guide');
        break;
      case 'shortcuts':
        navigate('/help/shortcuts');
        break;
      case 'about':
        navigate('/help/about');
        break;
      default:
        console.log('Menu action:', action);
    }
  };

  return (
    <div className="bg-white/10 backdrop-blur-md border-b border-white/10 shadow-sm relative z-50">
      <div className="flex items-center justify-between px-3 py-1.5">
        {/* Menu Items */}
        <div className="flex items-center space-x-1">
          {menus.map((menu) => (
            <div key={menu.name} className="relative">
              <button
                className={cn(
                  "px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200",
                  "hover:bg-white/20 hover:backdrop-blur-sm hover:shadow-sm",
                  "active:bg-white/30 active:scale-95",
                  activeMenu === menu.name && "bg-white/25 backdrop-blur-sm shadow-md"
                )}
                onClick={() => setActiveMenu(activeMenu === menu.name ? null : menu.name)}
                onMouseEnter={() => activeMenu && setActiveMenu(menu.name)}
              >
                {menu.name}
              </button>

              {activeMenu === menu.name && (
                <div className="absolute left-0 top-full z-50 bg-white/95 backdrop-blur-lg border border-white/20 shadow-xl rounded-lg min-w-[200px] py-2 mt-1">
                  {menu.items.map((item, index) => {
                    if (item.separator) {
                      return <div key={index} className="border-t border-gray-200/60 mx-2 my-1" />;
                    }
                    
                    return (
                      <div
                        key={index}
                        className="px-3 py-2 mx-1 flex items-center justify-between hover:bg-blue-50/80 hover:backdrop-blur-sm cursor-pointer rounded-md transition-all duration-150 group"
                        onClick={() => handleMenuItemClick(item.action)}
                      >
                        <div className="flex items-center gap-3">
                          {item.checked !== undefined && (
                            <span className="w-4 h-4 flex items-center justify-center text-blue-600">
                              {item.checked && "✓"}
                            </span>
                          )}
                          <span className="text-gray-700 group-hover:text-blue-700 font-medium">{item.name}</span>
                        </div>
                        {item.shortcut && (
                          <span className="text-gray-400 text-xs ml-4 group-hover:text-blue-500">{item.shortcut}</span>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          ))}
        </div>
        
        {/* User Info */}
        {user && (
          <div className="flex items-center space-x-2 px-3 py-1 bg-white/20 backdrop-blur-sm rounded-md">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-700">
              {user.username} ({user.role === 'ADMIN' ? 'Yönetici' : 'Memur'})
            </span>
          </div>
        )}
      </div>
      
      {/* Click outside to close menu */}
      {activeMenu && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setActiveMenu(null)}
        />
      )}
    </div>
  );
}
