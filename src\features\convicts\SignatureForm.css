/* Signature Form Styles for A4 Printing */
.signature-form-document {
  background: white;
  margin: 0 auto;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border: 1px solid #ddd;
}

.signature-form-page {
  width: 595px; /* A4 width in pixels at 96 DPI */
  min-height: 842px; /* A4 height in pixels at 96 DPI */
  margin: 0 auto;
  padding: 0;
  font-family: 'Inter', sans-serif;
  font-size: 10px;
  line-height: 1.2;
  color: #000;
  position: relative;
}

/* Document Title */
.signature-form-title {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  font-weight: 700;
  text-align: center;
  letter-spacing: 1px;
}

/* Header Section */
.signature-form-header {
  display: flex;
  width: 100%;
  height: 240px;
  border-top: 0.5px solid #000;
}

.signature-form-header-section {
  border-right: 0.5px solid #000;
  border-bottom: 0.5px solid #000;
  position: relative;
}

/* Header Label Section */
.signature-form-header-label {
  width: 31px;
  background-color: #DCDDEA;
  display: flex;
  align-items: center;
  justify-content: center;
}

.signature-form-header-title {
  font-size: 12px;
  font-weight: 700;
  text-align: center;
  transform: rotate(90deg);
  transform-origin: center;
  white-space: nowrap;
  padding: 10px 0;
}

/* Header Fields Section */
.signature-form-header-fields {
  width: 161px;
  background-color: #DCDDEA;
}

.signature-form-field {
  height: 40px;
  border-bottom: 0.5px solid #000;
  display: flex;
  align-items: center;
  padding: 0 11px;
}

.signature-form-field:last-child {
  border-bottom: none;
}

.signature-form-field-label {
  font-size: 10px;
  font-weight: 700;
}

/* Header Values Section */
.signature-form-header-values {
  width: 138px;
}

.signature-form-field-value {
  font-size: 8px;
  font-weight: 700;
  padding: 15px 4px;
}

/* Schedule Section */
.signature-form-schedule-label {
  width: 36px;
  background-color: #DCDDEA;
  display: flex;
  align-items: center;
  justify-content: center;
}

.signature-form-schedule {
  width: 161px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 4px;
  padding: 8px;
}

.signature-form-schedule-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
}

.signature-form-schedule-text {
  font-size: 8px;
  font-weight: normal;
  line-height: 1.3;
  text-align: center;
  margin: 1px 0;
}

/* Signature Sample Section */
.signature-form-sample {
  display: flex;
  width: 104px;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  text-align: center;
  gap: 40px;
}

.signature-form-sample-label {
  font-size: 10px;
  font-weight: 400;
  text-align: center;
  line-height: 1.2;
  margin-bottom: 20px;
}

.signature-form-sample-box {
  width: 104px;
  height: 89px;
  border: 0.5px solid #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.signature-form-sample-number {
  font-size: 40px;
  font-weight: 700;
  color: #000;
}

/* Table Section */
.signature-form-table {
  width: 100%;
  margin-top: 0px;
}

.signature-form-table-headers {
  width: 100%;
}

.signature-form-table-section-labels {
  display: flex;
  width: 100%;
  height: 22px;
  background-color: #DCDDEA;
}

.signature-form-table-section-label {
  border: 0.5px solid #000;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 400;
}

.signature-form-table-section-convict {
  width: 318px;
}

.signature-form-table-section-officer {
  width: 277px;
}

.signature-form-table-header-section {
  display: flex;
  width: 100%;
  height: 22px;
  background-color: #DCDDEA;
}

.signature-form-table-header {
  border: 0.5px solid #000;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 700;
  text-align: center;
}

.signature-form-table-header-sn {
  width: 30px;
}

.signature-form-table-header-date {
  width: 105px;
}

.signature-form-table-header-time {
  width: 82px;
}

.signature-form-table-header-convict-sig {
  width: 101px;
}

.signature-form-table-header-officer-name {
  width: 146px;
}

.signature-form-table-header-officer-sig {
  width: 131px;
}

/* Table Rows */
.signature-form-table-rows {
  width: 100%;
}

.signature-form-table-row {
  display: flex;
  width: 100%;
  height: 22px;
}

.signature-form-table-cell {
  border: 0.5px solid #000;
  display: flex;
  align-items: center;
  padding: 0 4px;
}

.signature-form-table-cell-sn {
  width: 30px;
  justify-content: center;
  font-size: 12px;
  font-weight: 400;
}

.signature-form-table-cell-date {
  width: 105px;
  font-size: 8px;
  font-weight: 400;
  padding-left: 8px;
}

.signature-form-table-cell-time {
  width: 82px;
}

.signature-form-table-cell-convict-sig {
  width: 101px;
}

.signature-form-table-cell-officer-name {
  width: 146px;
}

.signature-form-table-cell-officer-sig {
  width: 131px;
}

/* Print Styles */
@media print {
  @page {
    size: A4;
    margin: 0;
  }

  body {
    margin: 0;
    padding: 0;
  }

  /* Ensure signature form elements are visible during print */
  .signature-form-document,
  .signature-form-document *,
  .signature-form-page,
  .signature-form-page * {
    visibility: visible !important;
  }

  .signature-form-document {
    box-shadow: none;
    border: none;
    margin: 0;
    padding: 0;
  }

  .signature-form-page {
    width: 100%;
    min-height: 100vh;
    margin: 0;
    padding: 0;
  }

  .print\\:hidden {
    display: none !important;
  }

  .print\\:p-0 {
    padding: 0 !important;
  }

  .print\\:m-0 {
    margin: 0 !important;
  }

  .print\\:shadow-none {
    box-shadow: none !important;
  }

  .print\\:border-none {
    border: none !important;
  }
}

/* PDF generation styles */
.print-mode .signature-form-document {
  background: white !important;
  box-shadow: none !important;
  margin: 0 !important;
  padding: 0 !important;
  max-width: none !important;
  width: 210mm !important; /* A4 width */
  min-height: 297mm !important; /* A4 height */
}

.print-mode .signature-form-page {
  width: 100% !important;
  min-height: 100% !important;
  margin: 0 !important;
  padding: 20mm !important;
  font-size: 10px !important;
  line-height: 1.2 !important;
}

.print-mode .print\\:hidden {
  display: none !important;
}
