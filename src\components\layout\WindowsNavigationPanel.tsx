import { useState } from 'react';
import { Link, useLocation } from 'wouter';
import { useAuthStore } from '@/store/authStore';
import {
  HomeIcon,
  UsersIcon,
  PencilIcon,
  DocumentChartBarIcon,
  UserGroupIcon,
  Cog6ToothIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  FolderIcon,
  FolderOpenIcon,
  BookOpenIcon,
} from '@heroicons/react/24/outline';

interface NavigationChild {
  name: string;
  href: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
}

interface NavigationItem {
  name: string;
  href?: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  expandedIcon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  children?: NavigationChild[];
}

const navigationItems = [
  {
    name: 'Ana Say<PERSON>',
    href: '/dashboard',
    icon: HomeIcon,
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    icon: FolderIcon,
    expandedIcon: FolderOpenIcon,
    children: [
      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/convicts', icon: UsersIcon },
      { name: '<PERSON><PERSON>lü Ekle', href: '/convicts/new', icon: UsersIcon },
    ],
  },
  {
    name: 'İmza İşlemleri',
    icon: FolderIcon,
    expandedIcon: FolderOpenIcon,
    children: [
      { name: 'İmza Kaydı', href: '/signatures/record', icon: PencilIcon },
      { name: 'Beklenen İmzalar', href: '/signatures/expected', icon: PencilIcon },
      { name: 'Tamamlanan İmzalar', href: '/signatures/completed', icon: PencilIcon },
      { name: 'İmza Föyü - Ara', href: '/signature-form/search', icon: DocumentChartBarIcon },
    ],
  },
  {
    name: 'Raporlar',
    icon: FolderIcon,
    expandedIcon: FolderOpenIcon,
    children: [
      { name: 'Günlük Takip', href: '/reports/daily', icon: DocumentChartBarIcon },
      { name: 'Hükümlü Geçmişi', href: '/reports/convict-history', icon: DocumentChartBarIcon },
      { name: 'İhlal Raporu', href: '/reports/violations', icon: DocumentChartBarIcon },
    ],
  },  {
    name: 'Yardım',
    icon: FolderIcon,
    expandedIcon: FolderOpenIcon,
    children: [
      { name: 'Kullanım Kılavuzu', href: '/help/usage-guide', icon: BookOpenIcon },
      { name: 'Klavye Kısayolları', href: '/help/shortcuts', icon: BookOpenIcon },
    ],
  },
];

const adminNavigationItems = [
  {
    name: 'Yönetim',
    icon: FolderIcon,
    expandedIcon: FolderOpenIcon,
    children: [
      { name: 'Kullanıcı Yönetimi', href: '/admin/users', icon: UserGroupIcon },
      { name: 'Sistem Ayarları', href: '/admin/settings', icon: Cog6ToothIcon },
    ],
  },
];

export default function WindowsNavigationPanel() {
  const [location] = useLocation();
  const { user } = useAuthStore();
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({
    'Hükümlü Yönetimi': true,
    'İmza İşlemleri': true,
    'Raporlar': true,
    'Yönetim': true,
  });

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemName]: !prev[itemName],
    }));
  };

  const isActive = (href: string) => {
    return location === href || location.startsWith(href + '/');
  };

  const isParentActive = (children: NavigationChild[]) => {
    return children.some(child => isActive(child.href));
  };

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    if (item.children) {
      const isExpanded = expandedItems[item.name];
      const hasActiveChild = isParentActive(item.children);
      const IconComponent = isExpanded && item.expandedIcon ? item.expandedIcon : item.icon;

      return (
        <div key={item.name}>
          <button
            className={`w-full flex items-center px-3 py-2.5 text-left hover:bg-white/20 hover:backdrop-blur-sm focus:bg-white/25 transition-all duration-200 rounded-lg mx-2 group ${
              hasActiveChild ? 'bg-white/15 font-medium ring-1 ring-white/30' : ''
            }`}
            onClick={() => toggleExpanded(item.name)}
            style={{ paddingLeft: `${12 + level * 16}px` }}
          >
            <div className="w-4 h-4 mr-2 flex items-center justify-center">
              {isExpanded ? (
                <ChevronDownIcon className="w-3.5 h-3.5 text-slate-600 group-hover:text-slate-700 transition-colors" />
              ) : (
                <ChevronRightIcon className="w-3.5 h-3.5 text-slate-600 group-hover:text-slate-700 transition-colors" />
              )}
            </div>
            <IconComponent className="w-4 h-4 mr-3 text-slate-600 group-hover:text-slate-700 transition-colors" />
            <span className="text-sm text-slate-700 group-hover:text-slate-800 font-medium">{item.name}</span>
          </button>
          
          {isExpanded && (
            <div className="ml-2">
              {item.children.map((child: NavigationChild) => renderNavigationItem(child, level + 1))}
            </div>
          )}
        </div>
      );
    }

    const IconComponent = item.icon;
    const active = item.href ? isActive(item.href) : false;

    if (!item.href) return null;

    return (
      <Link key={item.href} href={item.href}>
        <div
          className={`flex items-center px-3 py-2.5 hover:bg-white/20 hover:backdrop-blur-sm focus:bg-white/25 transition-all duration-200 cursor-pointer rounded-lg mx-2 group ${
            active ? 'bg-white/25 border-l-2 border-blue-500 font-medium shadow-md ring-1 ring-white/30' : ''
          }`}
          style={{ paddingLeft: `${12 + level * 16}px` }}
        >
          <div className="w-4 h-4 mr-2"></div> {/* Spacer for alignment */}
          <IconComponent className={`w-4 h-4 mr-3 transition-colors ${
            active ? 'text-blue-600' : 'text-slate-600 group-hover:text-slate-700'
          }`} />
          <span className={`text-sm transition-colors ${
            active ? 'text-slate-800 font-medium' : 'text-slate-700 group-hover:text-slate-800'
          }`}>{item.name}</span>
        </div>
      </Link>
    );
  };

  return (
    <div className="w-64 bg-white/10 backdrop-blur-md border-r border-white/10 overflow-y-auto">
      {/* Header */}
      <div className="bg-white/15 backdrop-blur-lg border-b border-white/10 px-4 py-3">
        <h3 className="text-sm font-semibold text-slate-800">Navigasyon</h3>
      </div>
      
      {/* Navigation Tree */}
      <div className="py-3">
        {navigationItems.map(item => renderNavigationItem(item))}
        
        {/* Admin Section */}
        {user?.role === 'ADMIN' && (
          <>
            <div className="border-t border-white/20 mx-4 my-3"></div>
            {adminNavigationItems.map(item => renderNavigationItem(item))}
          </>
        )}
      </div>
    </div>
  );
}