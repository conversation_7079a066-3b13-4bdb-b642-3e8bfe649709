import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  InformationCircleIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  ComputerDesktopIcon,
  DocumentTextIcon,
  HeartIcon
} from '@heroicons/react/24/outline';

const AboutPage = () => {
  const features = [
    'Elektronik İmza Takibi',
    'Hükü<PERSON><PERSON><PERSON> Kayıt Yönetimi',
    'Otomatik Rapor Üretimi',
    'Excel İçe/Dışa Aktarma',
    'Gerçek Zamanlı Bildirimler',
    'Gü<PERSON>li Veri Saklama',
    '<PERSON>llan<PERSON><PERSON><PERSON> Yetki Yönetimi',
    'Detaylı Sistem Logları'
  ];
  const developers = [
    { name: '<PERSON><PERSON> GÜNGÖR', role: 'Proje Lideri & Full Stack Developer', email: '<EMAIL>' },
    { name: 'Ortaca Polis Merkezi <PERSON>', role: '<PERSON><PERSON><PERSON>', email: '' }
  ];

  const systemRequirements = [
    'Windows 10/11 (64-bit)',
    'RAM: Minimum 4GB, Önerilen 8GB',
    'Disk Alanı: 500MB',
    'İnternet Bağlantısı (Güncelleme için)',
    '.NET Framework 4.8 veya üzeri'
  ];

  return (
    <div className="h-full flex flex-col p-6 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-3">
          <InformationCircleIcon className="w-8 h-8 text-blue-600" />
          Hakkında - Elektronik İmza Takip Sistemi
        </h1>
        <p className="text-gray-600">
          EITS (Elektronik İmza Takip Sistemi) hakkında detaylı bilgiler
        </p>
      </div>      <ScrollArea className="flex-1">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Features */}
          <Card className="bg-white/80 backdrop-blur-sm border-green-200 shadow-lg">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg text-green-700">
                <ShieldCheckIcon className="w-5 h-5" />
                Özellikler
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-2">
                {features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 rounded-md hover:bg-green-50/80 transition-colors">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Development Team */}
          <Card className="bg-white/80 backdrop-blur-sm border-purple-200 shadow-lg">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg text-purple-700">
                <UserGroupIcon className="w-5 h-5" />
                Geliştirme Ekibi
              </CardTitle>
            </CardHeader>            <CardContent className="space-y-4">
              {developers.map((dev, index) => (
                <div key={index} className="p-3 rounded-lg bg-purple-50/80 border border-purple-100">
                  <h4 className="font-semibold text-gray-800">{dev.name}</h4>
                  <p className="text-sm text-purple-600 mb-1">{dev.role}</p>
                  {dev.email && <p className="text-xs text-gray-600">{dev.email}</p>}
                </div>
              ))}
            </CardContent>
          </Card>

          {/* System Requirements */}
          <Card className="bg-white/80 backdrop-blur-sm border-orange-200 shadow-lg">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg text-orange-700">
                <ComputerDesktopIcon className="w-5 h-5" />
                Sistem Gereksinimleri
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {systemRequirements.map((req, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 rounded-md hover:bg-orange-50/80 transition-colors">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-sm text-gray-700">{req}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* License and Legal */}
          <Card className="bg-white/80 backdrop-blur-sm border-gray-200 shadow-lg lg:col-span-2">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg text-gray-700">
                <DocumentTextIcon className="w-5 h-5" />
                Lisans ve Yasal Bilgiler
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-semibold text-gray-800">Telif Hakkı</h4>
                  <p className="text-sm text-gray-600">
                    © 2024 EITS Geliştirme Ekibi. Tüm hakları saklıdır.
                  </p>
                </div>                <div className="space-y-2">
                  <h4 className="font-semibold text-gray-800">Lisans Türü</h4>
                  <Badge variant="outline" className="bg-gray-100">
                    Özel Lisans
                  </Badge>
                </div>
              </div>
              
              <div className="border-t border-gray-200 pt-4">
                <p className="text-xs text-gray-500 leading-relaxed">
                  Bu yazılım, elektronik imza takip süreçlerini dijitalleştirmek ve yönetmek amacıyla geliştirilmiştir. 
                  Yazılımın kullanımı kurumsal lisans koşullarına tabidir. Yetkisiz kullanım, kopyalama veya dağıtım yasaktır.
                  Teknik destek ve güncellemeler için sistem yöneticiniz ile iletişime geçiniz.
                </p>
              </div>

              <div className="flex items-center justify-center pt-4 border-t border-gray-200">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span>Sevgiyle geliştirildi</span>
                  <HeartIcon className="w-4 h-4 text-red-500" />
                  <span>EITS Ekibi tarafından</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </ScrollArea>
    </div>
  );
};

export default AboutPage;
