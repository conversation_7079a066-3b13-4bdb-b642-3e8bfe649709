const Database = require('better-sqlite3');

console.log('🔧 Admin Role Fix Script');
console.log('=========================');

try {
  const db = new Database('./database.sqlite');
  
  // Check current admin user
  const adminUser = db.prepare('SELECT id, username, role FROM users WHERE username = ?').get('admin');
  
  if (adminUser) {
    console.log('📋 Current admin user:');
    console.log(`   ID: ${adminUser.id}`);
    console.log(`   Username: ${adminUser.username}`);
    console.log(`   Role: ${adminUser.role}`);
    
    if (adminUser.role !== 'ADMIN') {
      console.log('\n🔄 Updating admin role from "admin" to "ADMIN"...');
      
      const result = db.prepare('UPDATE users SET role = ? WHERE username = ?').run('ADMIN', 'admin');
      
      if (result.changes > 0) {
        console.log('✅ Admin role updated successfully!');
        
        // Verify the update
        const updatedUser = db.prepare('SELECT id, username, role FROM users WHERE username = ?').get('admin');
        console.log('📋 Updated admin user:');
        console.log(`   ID: ${updatedUser.id}`);
        console.log(`   Username: ${updatedUser.username}`);
        console.log(`   Role: ${updatedUser.role}`);
      } else {
        console.log('❌ Failed to update admin role');
      }
    } else {
      console.log('✅ Admin role is already correct (ADMIN)');
    }
  } else {
    console.log('❌ Admin user not found in database');
  }
  
  db.close();
  console.log('\n✅ Script completed successfully');
  
} catch (error) {
  console.error('❌ Error:', error.message);
  process.exit(1);
}
