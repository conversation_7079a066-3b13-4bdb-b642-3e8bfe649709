CREATE TABLE IF NOT EXISTS `settings` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`key` text NOT NULL,
	`value` text NOT NULL,
	`category` text NOT NULL,
	`data_type` text NOT NULL,
	`description` text,
	`created_at` text DEFAULT (datetime('now')),
	`updated_at` text DEFAULT (datetime('now'))
);
--> statement-breakpoint
CREATE UNIQUE INDEX IF NOT EXISTS `settings_key_unique` ON `settings` (`key`);
--> statement-breakpoint

-- Insert default settings
INSERT OR IGNORE INTO `settings` (`key`, `value`, `category`, `data_type`, `description`) VALUES
-- General Settings
('systemName', 'EİTS - Elektronik İmza Takip Sistemi', 'general', 'string', 'Sistem adı'),
('organizationName', 'Ortaca Polis Merkezi Amirliği', 'general', 'string', 'Kurum adı'),
('systemLanguage', 'tr', 'general', 'string', 'Sistem dili'),
('timeZone', 'Europe/Istanbul', 'general', 'string', 'Saat dilimi'),
('dateFormat', 'DD/MM/YYYY', 'general', 'string', 'Tarih formatı'),

-- Signature Settings
('signatureToleranceMinutes', '15', 'signature', 'number', 'İmza tolerans süresi (dakika)'),
('autoViolationDetection', 'true', 'signature', 'boolean', 'Otomatik ihlal tespiti'),
('requireReasonForLateSignature', 'true', 'signature', 'boolean', 'Geç imza için neden gerekli'),
('maxDailySignatures', '5', 'signature', 'number', 'Günlük maksimum imza sayısı'),

-- Security Settings
('sessionTimeoutMinutes', '480', 'security', 'number', 'Oturum zaman aşımı (dakika)'),
('passwordMinLength', '6', 'security', 'number', 'Minimum şifre uzunluğu'),
('requireStrongPassword', 'true', 'security', 'boolean', 'Güçlü şifre gerekli'),
('enableTwoFactorAuth', 'false', 'security', 'boolean', 'İki faktörlü kimlik doğrulama'),
('maxLoginAttempts', '3', 'security', 'number', 'Maksimum giriş denemesi'),
('lockoutDurationMinutes', '15', 'security', 'number', 'Hesap kilitleme süresi (dakika)'),

-- Notification Settings
('enableEmailNotifications', 'false', 'notification', 'boolean', 'E-posta bildirimleri'),
('emailServer', '', 'notification', 'string', 'E-posta sunucusu'),
('emailPort', '587', 'notification', 'number', 'E-posta portu'),
('emailUsername', '', 'notification', 'string', 'E-posta kullanıcı adı'),
('emailPassword', '', 'notification', 'string', 'E-posta şifresi'),
('enableSystemNotifications', 'true', 'notification', 'boolean', 'Sistem bildirimleri'),
('notificationSound', 'true', 'notification', 'boolean', 'Bildirim sesi'),

-- Data Management Settings
('autoBackupEnabled', 'true', 'data', 'boolean', 'Otomatik yedekleme'),
('backupIntervalDays', '7', 'data', 'number', 'Yedekleme aralığı (gün)'),
('dataRetentionDays', '1095', 'data', 'number', 'Veri saklama süresi (gün)'),

-- Display Settings
('recordsPerPage', '25', 'display', 'number', 'Sayfa başına kayıt sayısı'),
('enableAnimations', 'true', 'display', 'boolean', 'Animasyonlar'),
('compactMode', 'false', 'display', 'boolean', 'Kompakt mod'),
('showWelcomeMessage', 'true', 'display', 'boolean', 'Hoş geldin mesajı');
